// React core
import { useState, useEffect, useCallback } from "react";
// Third-party library imports
import { LoadBlock } from "microapps";
// Services
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
import { SpeechRecognitionService } from "./services/SpeechRecognitionService";
import { audioStateManager } from "./services/AudioStateManager";
import { conversationStateManager } from "./services/ConversationStateManager";
// Components
import { Header } from "./components/shared/Header";
import { <PERSON><PERSON> } from "./components/views/Cookie/Cookie";
import { Welcome } from "./components/views/Welcome/Welcome";
import { MainMenu } from "./components/views/MainMenu/MainMenu";
import { GameRules } from "./components/views/GameRules/GameRules";
import { Game } from "./components/views/Game/Game";
import { GameLive } from "./components/views/GameLive/GameLive";
import { GameHint } from "./components/views/GameHint/GameHint";
import { GameExit } from "./components/views/GameExit/GameExit";
import { GameEndScreen } from "./components/GameEndScreen";
import { DebugPanel } from "./components/DebugPanel";
// Utils & Constants & Helpers
import { GameStep, type GameStepType, GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { checkCookieConsent, saveCookieConsent } from "./utils/cookieUtils";
// Stores
import { useConversationStore } from "./stores/conversationStore";
// Styles
import "./App.scss";

function App() {
  // Step management - Initialize based on cookie consent
  const [currentStep, setCurrentStep] = useState<GameStepType>(() => {
    return checkCookieConsent() ? GameStep.WELCOME : GameStep.COOKIE_BANNER;
  });
  const [previousView, setPreviousView] = useState<GameStepType | null>(null);
  const [showRulesPopup, setShowRulesPopup] = useState<boolean>(false);

  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [gameWon, setGameWon] = useState<boolean>(false);
  const [, setCharacterError] = useState<string>("");

  // Character generation loading state
  const [isGeneratingCharacter, setIsGeneratingCharacter] = useState<boolean>(false);
  const [, setGenerationAttempt] = useState<number>(1);
  const [generationStep, setGenerationStep] = useState<string>("Preparando...");

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();
  const speechService = SpeechRecognitionService.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        const audioStartedCallback = appService.getAudioStartedCallback();

        playAudioWithFallback(
          audioUrl,
          audioFinishedCallback, // onEnded
          audioStartedCallback, // onStarted
          (error: string) => {
            // onError
            console.error("❌ Error reproduciendo audio:", error);
            if (audioFinishedCallback) {
              setTimeout(() => {
                audioFinishedCallback();
              }, 1000);
            }
          }
        );
      }, 100);
    });
  }, [appService]);

  /**
   * Stop all voice activities (microphone and AI transcription)
   * This function is called when navigating to MAIN_MENU to ensure
   * all voice-related activities are properly stopped
   */
  const stopAllVoiceActivities = useCallback(async () => {
    console.log("🔇 [MAIN_MENU] Iniciando detención completa de actividades de voz...");

    try {
      // 1. Stop speech recognition (microphone) - Force stop
      console.log("🎤 [MAIN_MENU] Deteniendo reconocimiento de voz...");
      speechService.stopListening();

      // 2. Disable smart microphone to prevent auto-reactivation
      console.log("🎤 [MAIN_MENU] Deshabilitando control inteligente del micrófono...");
      speechService.disableSmartMicrophoneControl();

      // 3. Stop any playing audio (AI transcription)
      console.log("🔊 [MAIN_MENU] Deteniendo audio de IA...");
      await audioStateManager.stopAudio();

      // 4. Reset conversation state manager
      console.log("🔄 [MAIN_MENU] Reseteando conversation state manager...");
      conversationStateManager.reset();

      // 5. Reset conversation store state
      console.log("🔄 [MAIN_MENU] Reseteando conversation store...");
      const conversationStore = useConversationStore.getState();
      conversationStore.reset();

      // 6. Clear any pending audio callbacks
      console.log("🔄 [MAIN_MENU] Limpiando callbacks de audio...");
      appService.setAudioFinishedCallback(() => {});
      appService.setAudioStartedCallback(() => {});

      // 7. Force stop any Web Speech API recognition that might still be running
      if (window.webkitSpeechRecognition || window.SpeechRecognition) {
        console.log("🎤 [MAIN_MENU] Verificando Web Speech API...");
        // Additional cleanup if needed
      }

      console.log("✅ [MAIN_MENU] Todas las actividades de voz han sido detenidas completamente");

    } catch (error) {
      console.error("❌ [MAIN_MENU] Error deteniendo actividades de voz:", error);
    }
  }, [speechService, appService]);

  /**
   * Monitor step changes and stop voice activities when reaching MAIN_MENU
   * This ensures that any ongoing voice interactions are properly terminated
   * when the user navigates back to the main menu
   */
  useEffect(() => {
    if (currentStep === GameStep.MAIN_MENU) {
      console.log("📍 [MAIN_MENU] Llegando a MAIN_MENU - verificando actividades de voz...");

      // Always stop voice activities when reaching MAIN_MENU, regardless of current state
      // This ensures a clean state and prevents any lingering voice activities
      console.log("🛑 [MAIN_MENU] Deteniendo todas las actividades de voz automáticamente...");

      // Use a small delay to ensure the step transition is complete
      setTimeout(() => {
        stopAllVoiceActivities();
      }, 100);
    }
  }, [currentStep, stopAllVoiceActivities]);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback(
    (response: any, fallback = "Respuesta no encontrada") => {
      return (
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        fallback
      );
    },
    []
  );

  /*********************************************************************************************
   * Step navigation functions
   *********************************************************************************************/

  const handleAcceptCookies = useCallback(() => {
    saveCookieConsent();
    setCurrentStep(GameStep.WELCOME);
  }, []);

  const handleDeclineCookies = useCallback(() => {
    setCurrentStep(GameStep.WELCOME);
  }, []);

  const handleAcceptAndPlay = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
  }, []);

  const navigateBack = useCallback(() => {
    if (previousView) {
      setCurrentStep(previousView);
      setPreviousView(null);
    } else {
      setCurrentStep(GameStep.MAIN_MENU);
    }
  }, [previousView]);

  const handleShowRules = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_RULES);
  }, [currentStep]);

  const handleShowGameLive = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_LIVE);
  }, [currentStep]);

  const handleShowGameHint = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_HINT);
  }, [currentStep]);

  const handleShowGameExit = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_EXIT);
  }, [currentStep]);

  /*********************************************************************************************
   * Game logic functions
   *********************************************************************************************/

  /**
   * Validate character against Movistar catalog using OTT Service
   * @param characterName - Name of the character to validate
   * @returns Promise<boolean> - true if character exists in catalog
   */
  const validateCharacterInCatalog = useCallback(async (characterName: string): Promise<boolean> => {
    try {
      console.log(`🔍 Validando personaje en catálogo OTT: ${characterName}`);

      // Use MovistarOttService for more intelligent search
      const { MovistarOttService } = await import('./services/MovistarOttService');
      const movistarService = new MovistarOttService();

      // Initialize the service
      await movistarService.init();

      // Query the agent for content about this character
      const query = `¿Hay alguna película o contenido disponible del actor o personaje ${characterName}? Solo responde con información específica si encuentras contenido real.`;
      const searchResult = await movistarService.sendToAgentFinalOnly(query);

      // Check if we got meaningful results (not generic responses)
      const hasResults = Boolean(searchResult &&
                        searchResult.length > 50 && // Minimum meaningful response length
                        !searchResult.includes("No se encontraron") &&
                        !searchResult.includes("no hay información") &&
                        !searchResult.includes("no tengo información") &&
                        !searchResult.includes("no puedo encontrar") &&
                        !searchResult.includes("Lo siento") &&
                        !searchResult.includes("(vacío)") &&
                        !searchResult.includes("Respuesta sin result") &&
                        (searchResult.includes("película") ||
                         searchResult.includes("serie") ||
                         searchResult.includes("contenido") ||
                         searchResult.includes("disponible") ||
                         searchResult.includes("Movistar")));

      console.log(`${hasResults ? '✅' : '❌'} Validación OTT de personaje: ${characterName} - ${hasResults ? 'ENCONTRADO' : 'NO ENCONTRADO'}`);
      console.log(`📝 Respuesta del agente: ${searchResult.substring(0, 200)}...`);

      return hasResults;
    } catch (error) {
      console.error("❌ Error validando personaje en catálogo OTT:", error);
      return false;
    }
  }, []);

  /**
   * Generate character with catalog validation
   * Generates characters until one is found in Movistar catalog
   * @param maxAttempts - Maximum number of attempts (default: 5)
   * @returns Promise<string> - Valid character name
   */
  const generateValidatedCharacter = useCallback(async (maxAttempts: number = 5): Promise<string> => {
    let attempts = 0;

    while (attempts < maxAttempts) {
      attempts++;
      setGenerationAttempt(attempts);
      setGenerationStep(`🎭 Generando personaje (${attempts}/${maxAttempts})...`);

      console.log(`🎭 Intento ${attempts}/${maxAttempts} - Generando personaje...`);

      try {
        // Generate character
        const characterResponse = await appService.generateWithGenCharBot(
          GAME_MESSAGES.GENERATE_CHARACTER
        );

        const characterText = extractResponseText(characterResponse);
        if (!characterText || characterText === "Respuesta no encontrada") {
          console.log(`❌ Intento ${attempts}: No se pudo generar personaje`);
          setGenerationStep(`❌ Error generando personaje, reintentando...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay before retry
          continue;
        }

        console.log(`🎭 Intento ${attempts}: Personaje generado - ${characterText}`);
        setGenerationStep(`🔍 Validando personaje...`);

        // Validate character in catalog
        const isValid = await validateCharacterInCatalog(characterText);

        if (isValid) {
          console.log(`✅ Personaje válido encontrado en intento ${attempts}: ${characterText}`);
          setGenerationStep(`✅ ¡Personaje validado! Preparando juego...`);
          return characterText;
        } else {
          console.log(`❌ Intento ${attempts}: Personaje no encontrado en catálogo - ${characterText}`);
          setGenerationStep(`❌ "${characterText}" no encontrado en catálogo, generando otro...`);
          await new Promise(resolve => setTimeout(resolve, 1500)); // Delay to show message
        }
      } catch (error) {
        console.error(`❌ Error en intento ${attempts}:`, error);
        setGenerationStep(`❌ Error en validación, reintentando...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error(`No se pudo generar un personaje válido después de ${maxAttempts} intentos`);
  }, [appService, extractResponseText, validateCharacterInCatalog]);

  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");
    setIsGeneratingCharacter(true);
    setGenerationAttempt(1);
    setGenerationStep("🚀 Iniciando generación de personaje...");

    try {
      // Step 1: Generate validated character
      console.log("🚀 Iniciando generación de personaje con validación de catálogo...");
      const characterText = await generateValidatedCharacter(5);

      setGeneratedCharacter(characterText);
      console.log(`✅ Personaje validado seleccionado: ${characterText}`);

      // Step 2: Start game immediately
      setGenerationStep("🎮 Iniciando juego...");
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);
      setGenerationStep("✅ ¡Juego listo!");
    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError(
        error instanceof Error
          ? error.message
          : "Error al generar personaje o iniciar juego. Inténtalo de nuevo."
      );
      setGameStarted(false);
      setGeneratedCharacter("");
      setGenerationStep("❌ Error en la generación");
    } finally {
      setAiLoading(false);
      // Hide loader after a short delay to show final message
      setTimeout(() => {
        setIsGeneratingCharacter(false);
      }, 1500);
    }
  }, [appService, extractResponseText, generateValidatedCharacter]);

  /**
   * Handle start game from main menu
   * Transitions to game playing step and starts the game
   */
  const handleStartGame = useCallback(() => {
    setCurrentStep(GameStep.GAME_PLAYING);
    handleStartGameDirectly();
  }, [handleStartGameDirectly]);

  /**
   * Handle game end
   * Transitions to game end step and stores the result
   */
  const handleGameEnd = useCallback((won: boolean) => {
    setGameWon(won);
    setCurrentStep(GameStep.GAME_END);
  }, []);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(async () => {
    console.log("🔄 [RESET] Iniciando reset del juego...");

    // First stop all voice activities before resetting
    await stopAllVoiceActivities();

    // Then reset all game state
    setCurrentStep(GameStep.MAIN_MENU);
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setGameWon(false);
    setCharacterError("");
    setShowRulesPopup(false);
    conversationStorage.clearAllConversations();

    console.log("✅ [RESET] Reset del juego completado");
  }, [conversationStorage, stopAllVoiceActivities]);

  /*********************************************************************************************
   * Render step content based on current step
   *********************************************************************************************/

  const renderStepContent = () => {
    switch (currentStep) {
      case GameStep.COOKIE_BANNER:
        return (
          <Cookie
            onAccept={handleAcceptCookies}
            onDecline={handleDeclineCookies}
          />
        );

      case GameStep.WELCOME:
        return (
          <Welcome
            handleAcceptAndPlay={handleAcceptAndPlay}
            aiLoading={aiLoading}
          />
        );

      case GameStep.MAIN_MENU:
        return (
          <MainMenu
            onStartGame={handleStartGame}
            onShowRules={handleShowRules}
            aiLoading={aiLoading}
          />
        );

      case GameStep.GAME_RULES:
        return <GameRules onStartGame={handleStartGame} />;

      case GameStep.GAME_PLAYING:
        return (
          <Game
            generatedCharacter={generatedCharacter}
            gameStarted={gameStarted}
            isGameStarted={gameStarted}
            initialMessage={initialMessage}
            onGameEnd={handleGameEnd}
            onShowGameLive={handleShowGameLive}
            onShowGameHint={handleShowGameHint}
            onShowGameExit={handleShowGameExit}
          />
        );

      case GameStep.GAME_LIVE:
        return <GameLive />;

      case GameStep.GAME_HINT:
        return <GameHint />;

      case GameStep.GAME_EXIT:
        return (
          <GameExit
            onConfirmExit={handleResetGame}
            onCancelExit={navigateBack}
          />
        );

      case GameStep.GAME_END:
        return (
          <GameEndScreen
            gameWon={gameWon}
            onClose={handleResetGame}
            onNewGame={handleResetGame}
            characterName={generatedCharacter}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      {currentStep !== GameStep.COOKIE_BANNER &&
        currentStep !== GameStep.WELCOME && (
          <Header
            currentStep={currentStep}
            onBackToMain={navigateBack}
            showBackButton={
              currentStep === GameStep.GAME_RULES ||
              currentStep === GameStep.GAME_HINT ||
              currentStep === GameStep.GAME_LIVE ||
              currentStep === GameStep.GAME_EXIT
            }
          />
        )}

      {renderStepContent()}

      {/* Character Generation Loader */}
      {isGeneratingCharacter && (
        <LoadBlock
          text={[
            `🎭 Generando personaje...`,
            "🔍 Validando...",
            "✨ Preparando experiencia de juego",
            generationStep
          ]}
          interval={1500}
          spinnerColor="#88FFD5"
          textColor="#ffffff"
          className="character-generation-loader"
          id="character-loader"
        />
      )}

      {/* Debug Panel - Only shown in development */}
      <DebugPanel
        currentStep={currentStep}
        showRulesPopup={showRulesPopup}
        aiLoading={aiLoading}
        generatedCharacter={generatedCharacter}
        gameStarted={gameStarted}
        initialMessage={initialMessage}
        gameWon={gameWon}
      />
    </>
  );
}

export default App;
