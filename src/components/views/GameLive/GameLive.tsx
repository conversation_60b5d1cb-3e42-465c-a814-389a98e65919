import React, { useEffect, useRef, useState } from "react";
// import "./GameLive.scss";
import { Modal } from "microapps";
import { ConversationStorage } from "../../../services/ConversationStorage";
import type { GameProgress as GameProgressType } from "../../../services/ConversationStorage";

interface GameLiveProps {
  onClose?: () => void;
  onConfirm?: () => void;
}

export const GameLive: React.FC<GameLiveProps> = ({ onClose, onConfirm }) => {
  // State for game progress
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);

  // Refs
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Update game progress when component mounts and periodically
  useEffect(() => {
    const updateProgress = () => {
      const progress = conversationStorage.current.getGameProgress();
      setGameProgress(progress);
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
  };

  // Create body content as string with emojis and formatting
  const createBodyContent = (): string => {
    if (!gameProgress) {
      return "Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada vez que haces una, se descuenta del contador. Piensa bien cada pregunta: ¡cada una cuenta!";
    }

    const questionsRemaining = gameProgress.questionsRemaining;
    const questionsAsked = gameProgress.questionsAsked;

    // Determine status emoji and message based on remaining questions
    let statusEmoji = "📊";
    let statusMessage = "";

    if (questionsRemaining === 0) {
      statusEmoji = "🚨";
      statusMessage = "\n\n🚨 ¡No te quedan más preguntas!";
    } else if (questionsRemaining <= 5) {
      statusEmoji = "⚠️";
      statusMessage = "\n\n⚠️ ¡Pocas preguntas restantes! Piensa bien cada una.";
    } else if (questionsRemaining <= 10) {
      statusEmoji = "🔶";
      statusMessage = "\n\n🔶 Vas por buen camino, pero ten cuidado.";
    } else {
      statusEmoji = "✅";
      statusMessage = "\n\n✅ Tienes suficientes preguntas restantes.";
    }

    return `
      Tienes un máximo de ${questionsRemaining} preguntas para adivinar la respuesta.
      Cada vez que haces una, se descuenta del contador.
      Piensa bien cada pregunta: ¡cada una cuenta!
    `;
  };

  return (
    <Modal
      title="Tus preguntas restantes"
      onClose={handleClose}
      onConfirm={handleConfirm}
      confirmText="Entendido"
      body={createBodyContent()}
    />
  );
};
