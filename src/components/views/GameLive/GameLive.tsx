import React, { useEffect, useRef, useState } from "react";
// import "./GameLive.scss";
import { Modal } from "microapps";
import { ConversationStorage } from "../../../services/ConversationStorage";
import type { GameProgress as GameProgressType } from "../../../services/ConversationStorage";

interface GameLiveProps {
  onClose?: () => void;
  onConfirm?: () => void;
}

/**
 * Obtiene el color de la cuenta regresiva basado en el número de preguntas restantes
 */
const getCountdownColor = (questionsRemaining: number): string => {
  if (questionsRemaining >= 16 && questionsRemaining <= 20) return "#FFFFFF";
  if (questionsRemaining >= 11 && questionsRemaining <= 15) return "#F6FFCB";
  if (questionsRemaining >= 6 && questionsRemaining <= 10) return "#EBFF88";
  if (questionsRemaining >= 1 && questionsRemaining <= 5) return "#FFD388";
  if (questionsRemaining === 0) return "#FF4548";
  return "#FFFFFF"; // Default
};

/**
 * Game Live Component
 *
 * Displays the current lives/attempts remaining in the game.
 * Features:
 * - Visual representation of remaining lives
 * - Game progress information
 * - Lives counter
 * - Real-time countdown display
 */
export const GameLive: React.FC<GameLiveProps> = ({ onClose, onConfirm }) => {
  // State for game progress
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);

  // Refs
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Update game progress when component mounts and periodically
  useEffect(() => {
    const updateProgress = () => {
      const progress = conversationStorage.current.getGameProgress();
      setGameProgress(progress);
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
  };



  // Custom modal content with countdown display
  if (!gameProgress) {
    return (
      <Modal
        title="Tus preguntas restantes"
        onClose={handleClose}
        onConfirm={handleConfirm}
        confirmText="Entendido"
        body="Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada vez que haces una, se descuenta del contador. Piensa bien cada pregunta: ¡cada una cuenta!"
      />
    );
  }

  // Custom modal with countdown visualization
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        zIndex: 1000,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "20px"
      }}
      onClick={handleClose}
    >
      <div
        style={{
          // backgroundColor: "white",
          // borderRadius: "12px",
          // padding: "24px",
          // maxWidth: "500px",
          // width: "100%",
          // maxHeight: "80vh",
          overflowY: "auto",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
          position: "relative"
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "20px",
          borderBottom: "1px solid #eee",
          paddingBottom: "15px"
        }}>
          <h3 style={{ margin: 0, color: "#333", fontSize: "18px", fontWeight: "600" }}>
            Tus preguntas restantes
          </h3>
          <button
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              color: "#666",
              padding: "5px"
            }}
            onClick={handleClose}
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div style={{ textAlign: "center" }}>
          <p style={{ marginBottom: "20px", color: "#555", lineHeight: "1.5" }}>
            Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada vez que haces una, se descuenta del contador. Piensa bien cada pregunta: ¡cada una cuenta!
          </p>

          <div style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "10px",
            padding: "20px",
            backgroundColor: "rgba(0, 0, 0, 0.05)",
            borderRadius: "12px",
            border: "2px solid rgba(0, 0, 0, 0.1)",
            marginBottom: "20px"
          }}>
            <div style={{
              fontSize: "48px",
              fontWeight: "bold",
              color: getCountdownColor(gameProgress.questionsRemaining),
              textShadow: "2px 2px 4px rgba(0, 0, 0, 0.3)",
              padding: "10px 20px",
              backgroundColor: "rgba(0, 0, 0, 0.1)",
              borderRadius: "50%",
              minWidth: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            }}>
              {gameProgress.questionsRemaining}
            </div>
            <div style={{ fontSize: "16px", fontWeight: "600", color: "#666" }}>
              Preguntas restantes
            </div>
            <div style={{ fontSize: "14px", color: "#888" }}>
              Has usado {gameProgress.questionsAsked} de 20 preguntas
            </div>
          </div>

          {/* Warning messages */}
          {gameProgress.questionsRemaining <= 5 && gameProgress.questionsRemaining > 0 && (
            <div style={{
              backgroundColor: "#fff3cd",
              color: "#856404",
              padding: "10px",
              borderRadius: "8px",
              marginBottom: "15px",
              border: "1px solid #ffeaa7"
            }}>
              ⚠️ ¡Pocas preguntas restantes! Piensa bien cada una.
            </div>
          )}

          {gameProgress.questionsRemaining === 0 && (
            <div style={{
              backgroundColor: "#f8d7da",
              color: "#721c24",
              padding: "10px",
              borderRadius: "8px",
              marginBottom: "15px",
              border: "1px solid #f5c6cb"
            }}>
              🚨 ¡No te quedan más preguntas!
            </div>
          )}

          {/* Confirm button */}
          <button
            style={{
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              padding: "12px 24px",
              borderRadius: "8px",
              fontSize: "16px",
              fontWeight: "600",
              cursor: "pointer",
              width: "100%",
              transition: "background-color 0.2s"
            }}
            onClick={handleConfirm}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#0056b3"}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = "#007bff"}
          >
            Entendido
          </button>
        </div>
      </div>
    </div>
  );
};
